// Copyright 2021 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React from "react";
import i18next from "i18next";
import * as Provider from "./Provider";
import UnifiedLoginButton from "./UnifiedLoginButton";
import * as AuthBackend from "./AuthBackend";
import {WechatOfficialAccountModal} from "./Util";
import * as Setting from "../Setting";

function getSigninButton(provider) {
  const text = i18next.t("login:Sign in with {type}").replace("{type}", provider.displayName !== "" ? provider.displayName : provider.type);
  return <UnifiedLoginButton provider={provider} text={text} align={"center"} />;
}

function goToSamlUrl(provider, location) {
  const params = new URLSearchParams(location.search);
  const clientId = params.get("client_id") ?? "";
  const state = params.get("state");
  const realRedirectUri = params.get("redirect_uri");
  const redirectUri = `${window.location.origin}/callback/saml`;
  const providerName = provider.name;

  const relayState = `${clientId}&${state}&${providerName}&${realRedirectUri}&${redirectUri}`;
  AuthBackend.getSamlLogin(`${provider.owner}/${providerName}`, btoa(relayState)).then((res) => {
    if (res.status === "ok") {
      if (res.data2 === "POST") {
        document.write(res.data);
      } else {
        window.location.href = res.data;
      }
    } else {
      Setting.showMessage("error", res.msg);
    }
  });
}

export function goToWeb3Url(application, provider, method) {
  if (provider.type === "MetaMask") {
    import("./Web3Auth")
      .then(module => {
        const authViaMetaMask = module.authViaMetaMask;
        authViaMetaMask(application, provider, method);
      });
  } else if (provider.type === "Web3Onboard") {
    import("./Web3Auth")
      .then(module => {
        const authViaWeb3Onboard = module.authViaWeb3Onboard;
        authViaWeb3Onboard(application, provider, method);
      });
  }
}

export function renderProviderLogo(provider, application, width, margin, size, location) {
  if (size === "small") {
    // 使用统一的小尺寸按钮样式，而不是简单的图标
    if (provider.category === "OAuth") {
      if (provider.type === "WeChat" && provider.clientId2 !== "" && provider.clientSecret2 !== "" && provider.disableSsl === true && !navigator.userAgent.includes("MicroMessenger")) {
        return (
          <div key={provider.displayName} className="provider-small-button" style={{margin: margin}} onClick={() => {
            WechatOfficialAccountModal(application, provider, "signup");
          }}>
            <UnifiedLoginButton provider={provider} size="small" />
          </div>
        );
      } else {
        return (
          <a key={provider.displayName} href={Provider.getAuthUrl(application, provider, "signup")} className="provider-small-button" style={{margin: margin}}>
            <UnifiedLoginButton provider={provider} size="small" />
          </a>
        );
      }
    } else if (provider.category === "SAML") {
      return (
        <div key={provider.displayName} className="provider-small-button" style={{margin: margin}} onClick={() => goToSamlUrl(provider, location)}>
          <UnifiedLoginButton provider={provider} size="small" />
        </div>
      );
    } else if (provider.category === "Web3") {
      return (
        <div key={provider.displayName} className="provider-small-button" style={{margin: margin}} onClick={() => goToWeb3Url(application, provider, "signup")}>
          <UnifiedLoginButton provider={provider} size="small" />
        </div>
      );
    }
  } else {
    // big button, for disable password signin
    if (provider.category === "SAML") {
      return (
        <div key={provider.displayName} className="provider-big-img">
          <a onClick={() => goToSamlUrl(provider, location)}>
            {
              getSigninButton(provider)
            }
          </a>
        </div>
      );
    } else if (provider.category === "Web3") {
      return (
        <div key={provider.displayName} className="provider-big-img">
          <a onClick={() => goToWeb3Url(application, provider, "signup")}>
            {
              getSigninButton(provider)
            }
          </a>
        </div>
      );
    } else {
      return (
        <div key={provider.displayName} className="provider-big-img">
          <a href={Provider.getAuthUrl(application, provider, "signup")}>
            {
              getSigninButton(provider)
            }
          </a>
        </div>
      );
    }
  }
}
