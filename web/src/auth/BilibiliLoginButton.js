// Copyright 2021 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {createButton} from "react-social-login-buttons";
import {StaticBaseUrl} from "../Setting";

function Icon() {
  return <img
    src={`${StaticBaseUrl}/buttons/bilibili.svg`}
    alt="Sign in with Bilibili"
    style={{
      filter: 'brightness(0) invert(1)', // 将图标颜色设置为白色
      width: '24px',
      height: '24px'
    }}
  />;
}

function BilibiliLoginButton({text = "Sign in with Bilibili", align = "center"}) {
  const config = {
    text: text,
    icon: Icon,
    iconFormat: name => `fa fa-${name}`,
    style: {
      background: "#0191e0",
      color: "#ffffff",
      fontWeight: "bold",
      width: "calc(50% - 10px)",
      margin: "5px",
      display: "inline-block",
    },
    activeStyle: {background: "#ffffff"},
  };

  const Button = createButton(config);
  return <Button text={text} align={align} />;
}

export default BilibiliLoginButton;
