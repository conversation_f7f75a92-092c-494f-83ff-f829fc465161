// Copyright 2025 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {createButton} from "react-social-login-buttons";
import {getProviderLogoURL} from "../Setting";
import {getProviderTheme} from "./ProviderThemes";
import i18next from "i18next";

function UnifiedLoginButton({provider, align = "center", size = "normal"}) {
  const theme = getProviderTheme(provider.type);
  const displayName = provider.name !== "" ? provider.name : theme.displayName || provider.type;
  function Icon({width = 24, height = 24}) {
    return (
      <img
        src={getProviderLogoURL(provider)}
        alt={`Sign in with ${displayName}`}
        style={{width: width, height: height}}
      />
    );
  }

  // Use the display name in the button text
  const buttonText = i18next.t("login:Sign in with {type}").replace("{type}", displayName);

  // 根据尺寸设置不同的样式
  const isSmall = size === "small";

  const config = {
    text: buttonText,
    icon: Icon,
    iconFormat: name => `fa fa-${name}`,
    style: {
      background: theme.background,
      color: theme.color,
      fontWeight: "bold",
      fontSize: isSmall ? "14px" : "16px",
      width: isSmall ? "auto" : "100%",
      height: isSmall ? "40px" : "48px",
      margin: isSmall ? "4px" : "8px 0",
      padding: isSmall ? "0 12px" : "0 16px",
      border: "none",
      borderRadius: "16px", // 使用与主题一致的圆角
      display: "inline-flex",
      alignItems: "center",
      justifyContent: "center",
      cursor: "pointer",
      transition: "all 0.3s ease",
      boxShadow: "none", // 移除阴影效果
      minWidth: isSmall ? "120px" : "auto",
    },
    activeStyle: {
      background: theme.activeBackground,
      boxShadow: "none", // 移除阴影效果
    },
  };

  const Button = createButton(config);

  return <Button text={buttonText} align={align} />;
}

export default UnifiedLoginButton;
